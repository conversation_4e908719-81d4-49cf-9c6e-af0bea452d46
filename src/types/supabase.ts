// Supabase Edge Function Types
// These types define the API contract for AI-powered conversation features

// Input structure for AI conversation edge function
export interface AIConversationRequest {
  sessionId: string;
  scenarioId: string;
  userMessage: string;
  conversationHistory: {
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }[];
  scenarioContext: {
    title: string;
    description: string;
    context: string;
    aiPersonality: string;
  };
  userProfile?: {
    id: string;
    preferences?: Record<string, any>;
  };
}

// Output structure for AI conversation edge function
export interface AIConversationResponse {
  success: boolean;
  data?: {
    aiResponse: string;
    feedback: {
      type: 'positive' | 'neutral' | 'negative';
      message: string;
      score: number; // 0-100
      reasoning: string;
    };
    suggestions?: string[];
    processingTime: number;
    responseId: string;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Input structure for feedback analysis edge function
export interface FeedbackAnalysisRequest {
  sessionId: string;
  messages: {
    content: string;
    sender: 'user' | 'ai';
    feedback?: {
      type: 'positive' | 'neutral' | 'negative';
      score: number;
    };
  }[];
}

// Output structure for feedback analysis edge function
export interface FeedbackAnalysisResponse {
  success: boolean;
  data?: {
    overallScore: number;
    breakdown: {
      positive: number;
      neutral: number;
      negative: number;
    };
    insights: {
      strengths: string[];
      improvements: string[];
      recommendations: string[];
    };
    trends: {
      scoreProgression: number[];
      improvementAreas: string[];
    };
  };
  error?: {
    code: string;
    message: string;
  };
}

// Database table types (for when Supabase is connected)
export interface DatabaseUser {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  preferences?: Record<string, any>;
}

export interface DatabaseChatSession {
  id: string;
  user_id: string;
  scenario_id: string;
  scenario_title: string;
  status: 'active' | 'completed';
  started_at: string;
  completed_at?: string;
  score_overall?: number;
  score_positive?: number;
  score_neutral?: number;
  score_negative?: number;
  total_messages?: number;
  created_at: string;
  updated_at: string;
}

export interface DatabaseMessage {
  id: string;
  session_id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  feedback_type?: 'positive' | 'neutral' | 'negative';
  feedback_message?: string;
  feedback_score?: number;
  exit_probability?: number;
  ai_response_id?: string;
  processing_time?: number;
  created_at: string;
}

export interface DatabaseScenario {
  id: string;
  title: string;
  description: string;
  context: string;
  ai_personality: string;
  icon: string;
  icon_color: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}