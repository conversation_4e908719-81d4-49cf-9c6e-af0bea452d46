
import React from 'react';
import { Platform, View, Text, ActivityIndicator } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Target, MessageCircle, Info, Users } from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../contexts/ThemeContext';
import { usePracticeStore } from '../stores/practiceStore';
import { useUserStore } from '../stores/userStore';
import { useSessionStore } from '../stores/sessionStore';
import { STORAGE_KEYS } from '../constants';
import { LoadingScreen } from '../components/ui/SkeletonLoader';

// Import screens
import LoginScreen from '../screens/LoginScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import PracticeHubScreen from '../screens/PracticeHubScreen';
import ChatScreen from '../screens/ChatScreen';
import PersonasScreen from '../screens/PersonasScreen';
import PersonaSelectionScreen from '../screens/PersonaSelectionScreen';
import ConversationScreen from '../screens/ConversationScreen';
import FeedbackScreen from '../screens/FeedbackScreen';
import AboutScreen from '../screens/AboutScreen';

// Define types for navigation
export type MainTabsParamList = {
  PracticeHub: undefined;
  Chat: undefined;
  Personas: undefined;
  About: undefined;
};

export type RootStackParamList = {
  MainTabs: undefined;
  PersonaSelectionScreen: {
    scenario: any;
    userId?: string;
  };
  ConversationScreen: undefined;
  FeedbackScreen: undefined;
};

const Tab = createBottomTabNavigator<MainTabsParamList>();
const Stack = createNativeStackNavigator<RootStackParamList>();

// Main tab navigator
const MainTabNavigator = () => {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          const iconProps = { size: 20, color };

          if (route.name === 'PracticeHub') {
            return <Target {...iconProps} />;
          } else if (route.name === 'Chat') {
            return <MessageCircle {...iconProps} />;
          } else if (route.name === 'Personas') {
            return <Users {...iconProps} />;
          } else if (route.name === 'About') {
            return <Info {...iconProps} />;
          }

          return <Target {...iconProps} />;
        },
        tabBarActiveTintColor: colors.PRIMARY_PINK,
        tabBarInactiveTintColor: colors.TEXT_SECONDARY,
        tabBarStyle: {
          height: Platform.OS === 'ios' ? 72 : 60,
          paddingBottom: 8,
          borderTopWidth: 0,
          elevation: 0,
          shadowOpacity: 0,
          backgroundColor: colors.CARD_WHITE,
          ...(Platform.OS === 'ios' ? { paddingBottom: 0 } : {}),
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        }
      })}
    >
      <Tab.Screen 
        name="PracticeHub" 
        component={PracticeHubScreen}
        options={{ tabBarLabel: 'Practice' }}
      />
      <Tab.Screen 
        name="Chat" 
        component={ChatScreen}
        options={{ tabBarLabel: 'Chat' }}
      />
      <Tab.Screen 
        name="Personas" 
        component={PersonasScreen}
        options={{ tabBarLabel: 'Personas' }}
      />
      <Tab.Screen 
        name="About" 
        component={AboutScreen}
        options={{ tabBarLabel: 'About' }}
      />
    </Tab.Navigator>
  );
};

// Root navigator with onboarding
export default function RootNavigator() {
  const { isAuthenticated, isLoading, loadUser } = useUserStore();
  const { loadScenarios } = useSessionStore();
  const isOnboarded = usePracticeStore(state => state.isOnboarded);
  const setOnboarded = usePracticeStore(state => state.setOnboarded);
  const [storeHydrated, setStoreHydrated] = React.useState(false);
  const [actuallyOnboarded, setActuallyOnboarded] = React.useState(false);
  
  // Wait for store to hydrate and check onboarding status
  React.useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        // Check both store and direct AsyncStorage
        const directOnboarded = await AsyncStorage.getItem(`${STORAGE_KEYS.PRACTICE_STORE}_onboarded`);
        const finalOnboardedState = isOnboarded || directOnboarded === 'true';
        
        console.log('🎯 Onboarding check:', { 
          storeOnboarded: isOnboarded, 
          directOnboarded: directOnboarded === 'true',
          finalState: finalOnboardedState
        });
        
        // Only update state if it's different to prevent unnecessary re-renders
        if (finalOnboardedState !== actuallyOnboarded) {
          setActuallyOnboarded(finalOnboardedState);
        }
        
        // If direct storage shows onboarded but store doesn't, sync them
        if (!isOnboarded && directOnboarded === 'true') {
          console.log('🎯 Syncing onboarding state from direct storage to store');
          setOnboarded(true);
        }
        
      } catch (error) {
        console.error('🎯 Error checking onboarding status:', error);
        if (isOnboarded !== actuallyOnboarded) {
          setActuallyOnboarded(isOnboarded);
        }
      }
      
      setStoreHydrated(true);
    };
    
    // Give stores time to hydrate from persistence
    const timer = setTimeout(checkOnboardingStatus, 100); // Reduced from 150ms
    
    return () => clearTimeout(timer);
  }, [isOnboarded, actuallyOnboarded]);
  
  // Debug logging for navigation state (debounced to prevent spam)
  React.useEffect(() => {
    const timer = setTimeout(() => {
      console.log('🎯 Navigation state:', { isAuthenticated, isLoading, isOnboarded, actuallyOnboarded, storeHydrated });
    }, 50);
    
    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, isOnboarded, actuallyOnboarded, storeHydrated]);

  // Load user and scenarios on app start
  React.useEffect(() => {
    loadUser();
    loadScenarios();
  }, []);

  // Show loading while checking auth or waiting for store hydration
  if (isLoading || !storeHydrated) {
    return <LoadingScreen message="Loading your app..." />;
  }

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return <LoginScreen />;
  }

  // Show onboarding for first-time users
  if (!actuallyOnboarded) {
    return <OnboardingScreen onComplete={() => setOnboarded(true)} />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      <Stack.Screen name="PersonaSelectionScreen" component={PersonaSelectionScreen} />
      <Stack.Screen name="ConversationScreen" component={ConversationScreen} />
      <Stack.Screen name="FeedbackScreen" component={FeedbackScreen} />
    </Stack.Navigator>
  );
}
