import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Sun, Moon, Monitor } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ThemeToggleProps {
  style?: any;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ style }) => {
  const { colors, mode, setTheme } = useTheme();

  const themes = [
    { key: 'light', label: 'Light', icon: Sun },
    { key: 'dark', label: 'Dark', icon: Moon },
    { key: 'system', label: 'System', icon: Monitor },
  ] as const;

  return (
    <View style={[styles.container, style]}>
      <Text style={[styles.title, { color: colors.TEXT_PRIMARY }]}>
        Theme
      </Text>
      <View style={styles.toggleContainer}>
        {themes.map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.themeOption,
              { 
                backgroundColor: mode === key ? colors.PRIMARY_PINK : colors.CARD_SOFT,
                borderColor: colors.BACKGROUND_DARK,
              }
            ]}
            onPress={() => setTheme(key)}
            activeOpacity={0.7}
          >
            <Icon 
              size={18} 
              color={mode === key ? colors.CARD_WHITE : colors.TEXT_SECONDARY} 
            />
            <Text 
              style={[
                styles.themeLabel,
                { 
                  color: mode === key ? colors.CARD_WHITE : colors.TEXT_SECONDARY 
                }
              ]}
            >
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  toggleContainer: {
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
    gap: 2,
  },
  themeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 10,
    borderWidth: 1,
    gap: 6,
  },
  themeLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
});
