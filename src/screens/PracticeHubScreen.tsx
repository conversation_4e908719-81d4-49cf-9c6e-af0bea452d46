import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { Heart, Calendar, Coffee, RefreshCw } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { COLORS } from '../constants/colors';
import { useSessionStore } from '../stores/sessionStore';
import { useUserStore } from '../stores/userStore';
import { ScenarioCardSkeleton, ProgressCardSkeleton, SkeletonLoader } from '../components/ui/SkeletonLoader';

interface PracticeHubScreenProps {
  navigation: any;
}

export default function PracticeHubScreen({ navigation }: PracticeHubScreenProps) {
  const insets = useSafeAreaInsets();
  const { scenarios, startNewSession } = useSessionStore();
  const { user, localProgress } = useUserStore();
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [creatingScenarioId, setCreatingScenarioId] = useState<string | null>(null);

  const getIconComponent = (iconName: string, color: string) => {
    const iconProps = { size: 24, color: COLORS.CARD_WHITE };
    
    switch (iconName) {
      case 'heart':
        return <Heart {...iconProps} />;
      case 'calendar':
        return <Calendar {...iconProps} />;
      case 'coffee':
        return <Coffee {...iconProps} />;
      case 'refresh-cw':
        return <RefreshCw {...iconProps} />;
      default:
        return <Heart {...iconProps} />;
    }
  };

  const handleScenarioPress = async (scenario: any) => {
    // Navigate to persona selection instead of starting session immediately
    navigation.navigate('PersonaSelectionScreen', { 
      scenario: scenario,
      userId: user?.id 
    });
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={styles.headerTitle}>Practice</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Choose Your Practice Scenario</Text>
          <Text style={styles.welcomeSubtitle}>
            Select a conversation scenario and get instant AI feedback on every message
          </Text>
        </View>

        {/* Scenario Cards */}
        <View style={styles.scenariosContainer}>
          {scenarios.length === 0 ? (
            // Show skeleton loaders while scenarios are loading
            Array.from({ length: 3 }).map((_, index) => (
              <ScenarioCardSkeleton key={`skeleton-${index}`} />
            ))
          ) : (
            scenarios.map((scenario) => (
            <View key={scenario.id} style={styles.scenarioCard}>
              <View style={styles.scenarioHeader}>
                <View style={[styles.iconContainer, { backgroundColor: scenario.icon_color }]}>
                  {getIconComponent(scenario.icon, scenario.icon_color)}
                </View>
                <Text style={styles.scenarioTitle}>{scenario.title}</Text>
              </View>
              <Text style={styles.scenarioDescription}>{scenario.description}</Text>
              <TouchableOpacity 
                style={[styles.startButton, (isCreatingSession && creatingScenarioId === scenario.id) && styles.startButtonDisabled]}
                onPress={() => handleScenarioPress(scenario)}
                disabled={isCreatingSession}
              >
                {isCreatingSession && creatingScenarioId === scenario.id ? (
                  <View style={styles.loadingContainer}>
                    <SkeletonLoader width={200} height={16} style={{ backgroundColor: 'rgba(255,255,255,0.3)' }} />
                  </View>
                ) : (
                  <Text style={styles.startButtonText}>Start Practice</Text>
                )}
              </TouchableOpacity>
            </View>
            ))
          )}
        </View>

        {/* Progress Section */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>Your Progress</Text>
          <View style={styles.progressGrid}>
            <View style={styles.progressCard}>
              <Text style={styles.progressNumber}>{localProgress.scenariosCompleted}</Text>
              <Text style={styles.progressLabel}>Scenarios Completed</Text>
            </View>
            <View style={styles.progressCard}>
              <Text style={[styles.progressNumber, { color: COLORS.SECONDARY_TEAL }]}>
                {localProgress.messagesPracticed}
              </Text>
              <Text style={styles.progressLabel}>Messages Practiced</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_GRAY,
  },
  header: {
    backgroundColor: COLORS.CARD_WHITE,
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    letterSpacing: -0.4,
  },
  logoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fce7f3',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 100,
  },
  welcomeSection: {
    marginBottom: 32,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
  },
  scenariosContainer: {
    marginBottom: 32,
  },
  scenarioCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  scenarioHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  scenarioTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  scenarioDescription: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
    marginBottom: 16,
  },
  startButton: {
    backgroundColor: COLORS.PRIMARY_PINK,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  startButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  startButtonDisabled: {
    opacity: 0.7,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressSection: {
    marginBottom: 24,
  },
  progressTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  progressGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  progressCard: {
    flex: 1,
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  progressNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.PRIMARY_PINK,
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 18,
  },
});