import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { CheckCircle, ThumbsUp, Lightbulb } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useSessionStore } from '../stores/sessionStore';
import { useUserStore } from '../stores/userStore';

interface FeedbackScreenProps {
  navigation: any;
}

export default function FeedbackScreen({ navigation }: FeedbackScreenProps) {
  const insets = useSafeAreaInsets();
  const { currentSession, messages, startNewSession, getScenarioById } = useSessionStore();
  const { incrementScenarioCompleted, user } = useUserStore();
  const { colors } = useTheme();

  const handleTryAgain = async () => {
    if (currentSession) {
      const scenario = getScenarioById(currentSession.scenarioId);
      if (scenario) {
        await startNewSession(scenario, user?.id);
        navigation.replace('ConversationScreen');
      }
    }
  };

  const handleNewScenario = () => {
    if (currentSession) {
      incrementScenarioCompleted(currentSession.scenarioId);
    }
    navigation.replace('MainTabs', { screen: 'PracticeHub' });
  };

  const handleGoBack = () => {
    navigation.replace('MainTabs', { screen: 'PracticeHub' });
  };

  const userMessages = messages.filter(msg => msg.sender === 'user');
  const positiveCount = userMessages.filter(msg => msg.feedback?.type === 'positive').length;
  const neutralCount = userMessages.filter(msg => msg.feedback?.type === 'neutral').length;
  const negativeCount = userMessages.filter(msg => msg.feedback?.type === 'negative').length;

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND_GRAY }]}>
      {/* Header with Back Button */}
      <View style={[styles.topHeader, { paddingTop: insets.top, backgroundColor: colors.CARD_WHITE }]}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
            <Text style={[styles.backButtonText, { color: colors.PRIMARY_PINK }]}>‹</Text>
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Text style={[styles.headerTitle, { color: colors.TEXT_PRIMARY }]}>Practice Complete</Text>
          </View>
          <View style={styles.headerSpacer} />
        </View>
      </View>

      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.successIcon}>
            <CheckCircle size={32} color={COLORS.CARD_WHITE} />
          </View>
          <Text style={styles.title}>Great Practice!</Text>
          <Text style={styles.subtitle}>Here's your conversation analysis</Text>
        </View>

        {/* What Worked Well */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIcon}>
              <ThumbsUp size={20} color={COLORS.CARD_WHITE} />
            </View>
            <Text style={styles.sectionTitle}>What Worked Well</Text>
          </View>
          
          <View style={styles.feedbackList}>
            {positiveCount > 0 && (
              <Text style={styles.feedbackItem}>
                • You asked thoughtful follow-up questions that showed genuine interest
              </Text>
            )}
            {userMessages.length > 2 && (
              <Text style={styles.feedbackItem}>
                • Your messages had a good balance of humor and sincerity
              </Text>
            )}
            {userMessages.some(msg => msg.content.length > 20 && msg.content.length < 100) && (
              <Text style={styles.feedbackItem}>
                • You maintained appropriate message length - not too short or overwhelming
              </Text>
            )}
            {userMessages.some(msg => msg.content.toLowerCase().includes('i ') || msg.content.toLowerCase().includes('my ')) && (
              <Text style={styles.feedbackItem}>
                • You shared personal experiences which helps build connection
              </Text>
            )}
          </View>
        </View>

        {/* Areas to Improve */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={[styles.sectionIcon, { backgroundColor: COLORS.WARNING_YELLOW }]}>
              <Lightbulb size={20} color={COLORS.CARD_WHITE} />
            </View>
            <Text style={styles.sectionTitle}>Areas to Improve</Text>
          </View>
          
          <View style={styles.feedbackList}>
            {negativeCount > 0 && (
              <Text style={styles.feedbackItem}>
                • Try to share more personal experiences to build deeper connection
              </Text>
            )}
            {!userMessages.some(msg => msg.content.includes('?')) && (
              <Text style={styles.feedbackItem}>
                • Consider asking more questions to keep the conversation flowing
              </Text>
            )}
            {userMessages.some(msg => msg.content.length < 15) && (
              <Text style={styles.feedbackItem}>
                • Try to add more detail to your messages for richer conversation
              </Text>
            )}
            {neutralCount > positiveCount && (
              <Text style={styles.feedbackItem}>
                • Consider using more emojis to convey tone and emotion
              </Text>
            )}
          </View>
        </View>

        {/* Statistics */}
        <View style={styles.statsSection}>
          <Text style={styles.statsTitle}>Your Performance</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: COLORS.SUCCESS_GREEN }]}>{positiveCount}</Text>
              <Text style={styles.statLabel}>Great Messages</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: COLORS.WARNING_YELLOW }]}>{neutralCount}</Text>
              <Text style={styles.statLabel}>Good Messages</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: COLORS.ERROR_RED }]}>{negativeCount}</Text>
              <Text style={styles.statLabel}>Needs Work</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.primaryButton} onPress={handleTryAgain}>
          <Text style={styles.primaryButtonText}>Try This Scenario Again</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.secondaryButton} onPress={handleNewScenario}>
          <Text style={styles.secondaryButtonText}>Start New Scenario</Text>
        </TouchableOpacity>
      </View>

      {/* Footer */}
      <Text style={styles.footer}>Powered by Magically</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_GRAY,
  },
  topHeader: {
    backgroundColor: COLORS.CARD_WHITE,
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  backButtonText: {
    fontSize: 28,
    color: COLORS.PRIMARY_PINK,
    fontWeight: '300',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    letterSpacing: -0.4,
  },
  headerSpacer: {
    width: 44,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  successIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: COLORS.SUCCESS_GREEN,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  section: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.SUCCESS_GREEN,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  feedbackList: {
    gap: 8,
  },
  feedbackItem: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  statsSection: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  actionButtons: {
    paddingHorizontal: 24,
    paddingBottom: 16,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY_PINK,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: COLORS.PRIMARY_PINK,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: COLORS.SECONDARY_TEAL,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: COLORS.SECONDARY_TEAL,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  secondaryButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    paddingBottom: 8,
  },
});