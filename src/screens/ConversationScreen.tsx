import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Heart, Send, User, Bot, Check, Minus, X } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useSessionStore } from '../stores/sessionStore';
import { useUserStore } from '../stores/userStore';
import { LoadingScreen, MessageSkeleton, AITypingIndicator } from '../components/ui/SkeletonLoader';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import ProfileHeader from '../components/ui/ProfileHeader';
import BottomSheet from '../components/ui/BottomSheet';
import PersonaDetails from '../components/ui/PersonaDetails';
import ScenarioDetails from '../components/ui/ScenarioDetails';
import FeedbackDetails from '../components/ui/FeedbackDetails';

interface ConversationScreenProps {
  navigation: any;
}

export default function ConversationScreen({ navigation }: ConversationScreenProps) {
  const insets = useSafeAreaInsets();
  const { currentSession, currentScenario, currentPersona, messages, addMessage, generateAIResponse, isAITyping, endCurrentSession } = useSessionStore();
  const { incrementMessagePracticed, updateScore } = useUserStore();
  const { colors } = useTheme();
  const [inputText, setInputText] = useState('');
  const [showEndModal, setShowEndModal] = useState(false);
  const [showPersonaSheet, setShowPersonaSheet] = useState(false);
  const [showScenarioSheet, setShowScenarioSheet] = useState(false);
  const [showFeedbackSheet, setShowFeedbackSheet] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState<any>(null);
  const [selectedMessage, setSelectedMessage] = useState<string>('');
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (!currentSession || !currentScenario) {
      navigation.goBack();
      return;
    }
  }, [currentSession, currentScenario]);

  // Auto-scroll when new messages are added or AI starts/stops typing
  useEffect(() => {
    const scrollToBottom = () => {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    };
    
    scrollToBottom();
  }, [messages.length, isAITyping]);

  // Smooth scroll when AI typing state changes
  useEffect(() => {
    if (isAITyping) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 150);
    }
  }, [isAITyping]);

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessageText = inputText.trim();
    setInputText('');

    // Add user message first (without feedback initially)
    const messageId = await addMessage(userMessageText, 'user');
    incrementMessagePracticed();

    // Immediate scroll after user message
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 50);

    // Generate AI response (which will also provide feedback and update score)
    await generateAIResponse(userMessageText, messageId);

    // Additional scroll after AI response with longer delay for content to render
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 200);
  };

  const getFeedbackIcon = (type: string) => {
    switch (type) {
      case 'positive':
        return <Check size={16} color={colors.CARD_WHITE} />;
      case 'neutral':
        return <Minus size={16} color={colors.CARD_WHITE} />;
      case 'negative':
        return <X size={16} color={colors.CARD_WHITE} />;
      default:
        return <Check size={16} color={colors.CARD_WHITE} />;
    }
  };

  const handleEndPractice = () => {
    setShowEndModal(true);
  };

  const confirmEndPractice = async () => {
    setShowEndModal(false);
    await endCurrentSession();
    navigation.replace('FeedbackScreen');
  };

  const handleFeedbackPress = (feedback: any, messageContent: string) => {
    setSelectedFeedback(feedback);
    setSelectedMessage(messageContent);
    setShowFeedbackSheet(true);
  };

  if (!currentScenario) {
    return null;
  }
  
  // Show loading if persona is still being loaded
  if (!currentPersona) {
    return <LoadingScreen message="Loading conversation partner..." />;
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Text style={styles.backButtonText}>‹</Text>
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Text style={styles.headerTitle}>Practice Session</Text>
          </View>
          <TouchableOpacity onPress={handleEndPractice} style={styles.endButton}>
            <Text style={styles.endButtonText}>End</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Profile Header with Scenario Swiper */}
      <ProfileHeader
        persona={currentPersona}
        scenario={currentScenario}
        onPersonaPress={() => setShowPersonaSheet(true)}
        onScenarioPress={() => setShowScenarioSheet(true)}
        isLoading={!currentPersona || !currentScenario}
      />

      {/* Chat Messages */}
      <ScrollView 
        ref={scrollViewRef}
        style={styles.chatContainer}
        contentContainerStyle={styles.chatContent}
      >
        {messages.map((message) => (
          <View key={message.id}>
            {message.sender === 'system' ? (
              // System message (chat ended)
              <View style={styles.systemMessageContainer}>
                <Text style={styles.systemMessageText}>{message.content}</Text>
              </View>
            ) : (
              <View style={[
                styles.messageContainer,
                message.sender === 'user' ? styles.userMessageContainer : styles.aiMessageContainer
              ]}>
                <View style={[
                  styles.messageAvatar,
                  message.sender === 'user' ? styles.userAvatar : styles.aiAvatar
                ]}>
                  {message.sender === 'user' ? 
                    <User size={16} color={COLORS.CARD_WHITE} /> : 
                    <Bot size={16} color={COLORS.CARD_WHITE} />
                  }
                </View>
                <View style={[
                  styles.messageBubble,
                  message.sender === 'user' ? styles.userMessage : styles.aiMessage
                ]}>
                  <Text style={[
                    styles.messageText,
                    message.sender === 'user' ? styles.userMessageText : styles.aiMessageText
                  ]}>
                    {message.content}
                  </Text>
                </View>
              </View>
            )}
            
            {/* Compact Feedback for user messages */}
            {message.sender === 'user' && message.feedback && (
              <TouchableOpacity 
                onPress={() => handleFeedbackPress(message.feedback, message.content)}
                activeOpacity={0.8}
              >
                <View style={[styles.compactFeedbackBubble, { backgroundColor: message.feedback.color }]}>
                  <View style={styles.feedbackIcon}>
                    {getFeedbackIcon(message.feedback.type)}
                  </View>
                  <Text style={styles.compactFeedbackText} numberOfLines={2}>{message.feedback.message}</Text>
                  <View style={styles.feedbackScores}>
                    {message.feedback.score !== undefined && message.feedback.score !== null && (
                      <Text style={styles.feedbackScore}>{message.feedback.score}</Text>
                    )}
                    {message.feedback.exitProbability !== undefined && message.feedback.exitProbability > 0 && (
                      <Text style={[styles.feedbackScore, styles.exitProbabilityScore]}>
                        {message.feedback.exitProbability}% exit
                      </Text>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            )}
          </View>
        ))}

        {/* AI Typing Indicator */}
        {isAITyping && (
          <MessageSkeleton />
        )}
      </ScrollView>

      {/* Message Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="Type your message..."
          placeholderTextColor={COLORS.TEXT_SECONDARY}
          multiline
          maxLength={500}
        />
        <TouchableOpacity 
          style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
          onPress={handleSendMessage}
          disabled={!inputText.trim()}
        >
          <Send size={20} color={COLORS.CARD_WHITE} />
        </TouchableOpacity>
      </View>

      {/* End Conversation Modal */}
      <ConfirmationModal
        visible={showEndModal}
        title="End Practice Session?"
        message="Are you sure you want to end this conversation? You'll see your feedback and analysis."
        confirmText="End Session"
        cancelText="Continue"
        onConfirm={confirmEndPractice}
        onCancel={() => setShowEndModal(false)}
        confirmColor={COLORS.PRIMARY_PINK}
      />

      {/* Persona Details Bottom Sheet */}
      <BottomSheet
        visible={showPersonaSheet}
        onClose={() => setShowPersonaSheet(false)}
        title="Conversation Partner"
      >
        <PersonaDetails persona={currentPersona} />
      </BottomSheet>

      {/* Scenario Details Bottom Sheet */}
      <BottomSheet
        visible={showScenarioSheet}
        onClose={() => setShowScenarioSheet(false)}
        title="Practice Scenario"
      >
        <ScenarioDetails scenario={currentScenario} />
      </BottomSheet>

      {/* Feedback Details Bottom Sheet */}
      <BottomSheet
        visible={showFeedbackSheet}
        onClose={() => setShowFeedbackSheet(false)}
        title="Message Feedback"
      >
        {selectedFeedback && (
          <FeedbackDetails 
            feedback={selectedFeedback} 
            message={selectedMessage}
          />
        )}
      </BottomSheet>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_LIGHT,
  },
  header: {
    backgroundColor: COLORS.CARD_WHITE,
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  backButtonText: {
    fontSize: 28,
    color: COLORS.PRIMARY_PINK,
    fontWeight: '300',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    letterSpacing: -0.4,
  },
  endButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'flex-end',
    backgroundColor: COLORS.SECONDARY_TEAL,
    borderRadius: 8,
  },
  endButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 14,
    fontWeight: '600',
  },
  contextCard: {
    backgroundColor: COLORS.CARD_WHITE,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contextHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contextIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.SECONDARY_TEAL,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  contextLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  contextText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  compactContextCard: {
    backgroundColor: COLORS.CARD_WHITE,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  compactContextText: {
    fontSize: 13,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 18,
    flex: 1,
    marginLeft: 8,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_LIGHT,
  },
  chatContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 24,
  },
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  aiMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  userAvatar: {
    backgroundColor: COLORS.PRIMARY_PINK,
  },
  aiAvatar: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 16,
  },
  userMessage: {
    backgroundColor: COLORS.CHAT_BUBBLE_USER,
    borderBottomRightRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  aiMessage: {
    backgroundColor: COLORS.CHAT_BUBBLE_AI,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: COLORS.CHAT_BUBBLE_AI_BORDER,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: COLORS.CARD_WHITE,
  },
  aiMessageText: {
    color: COLORS.TEXT_PRIMARY,
  },
  feedbackBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 48,
    marginRight: 16,
    marginTop: 4,
    marginBottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
  },
  feedbackIcon: {
    marginRight: 8,
  },
  feedbackText: {
    color: COLORS.CARD_WHITE,
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  compactFeedbackBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 48,
    marginRight: 12,
    marginTop: 2,
    marginBottom: 6,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  endButtonText: {
    fontSize: 16,
    color: COLORS.PRIMARY_PINK,
    fontWeight: '600',
    borderRadius: 8,
  },
  compactFeedbackText: {
    color: COLORS.CARD_WHITE,
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
    lineHeight: 16,
  },
  feedbackScores: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    marginLeft: 8,
  },
  feedbackScore: {
    color: COLORS.CARD_WHITE,
    fontSize: 12,
    fontWeight: 'bold',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginBottom: 2,
  },
  exitProbabilityScore: {
    backgroundColor: 'rgba(255,255,255,0.3)',
    fontSize: 10,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  typingBubble: {
    backgroundColor: COLORS.CARD_WHITE,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  typingText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: 14,
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 24,
    backgroundColor: COLORS.CARD_SOFT,
    borderTopWidth: 1,
    borderTopColor: COLORS.BACKGROUND_DARK,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.BACKGROUND_DARK,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    backgroundColor: COLORS.CARD_WHITE,
    maxHeight: 100,
    marginRight: 12,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.PRIMARY_PINK,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: 12,
    paddingHorizontal: 20,
  },
  systemMessageText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    fontStyle: 'italic',
    backgroundColor: COLORS.BACKGROUND_GRAY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
});