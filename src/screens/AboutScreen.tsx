
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Linking, Image } from 'react-native';
import { Heart, Target, MessageCircle, Lightbulb, Sparkles, User, LogOut, Mail } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useUserStore } from '../stores/userStore';
import { SkeletonLoader } from '../components/ui/SkeletonLoader';
import { ThemeToggle } from '../components/ui/ThemeToggle';
import SignOutModal from '../components/ui/SignOutModal';
import { COLORS } from '../constants/colors';

export default function AboutScreen() {
  const insets = useSafeAreaInsets();
  const [isLoading, setIsLoading] = useState(false);
  const [showSignOutModal, setShowSignOutModal] = useState(false);
  const { user, isAuthenticated, signOut } = useUserStore();
  const { colors } = useTheme();
  
  const handleMagicallyPress = () => {
    Linking.openURL('https://trymagically.com');
  };

  const handleSignOut = () => {
    console.log('🚪 Button pressed - showing signout modal');
    setShowSignOutModal(true);
  };

  const handleConfirmSignOut = async () => {
    console.log('🚪 User confirmed signout');
    setIsLoading(true);
    try {
      console.log('🚪 Calling signOut function...');
      await signOut();
      console.log('✅ SignOut completed successfully');
      setShowSignOutModal(false);
    } catch (error) {
      console.error('❌ Sign out error:', error);
      // Keep modal open and show error state
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelSignOut = () => {
    console.log('🚪 User cancelled signout');
    setShowSignOutModal(false);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND_GRAY }]}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top, backgroundColor: colors.CARD_WHITE }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={[styles.headerTitle, { color: colors.TEXT_PRIMARY }]}>About</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Profile Section */}
        {isAuthenticated && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile</Text>
            
            <View style={styles.profileCard}>
              {user ? (
                <View style={styles.profileHeader}>
                  <View style={styles.avatarContainer}>
                    {user.avatar_url ? (
                      <Image source={{ uri: user.avatar_url }} style={styles.avatar} />
                    ) : (
                      <User size={32} color={colors.TEXT_SECONDARY} />
                    )}
                  </View>
                  <View style={styles.profileInfo}>
                    <Text style={[styles.profileName, { color: colors.TEXT_PRIMARY }]}>
                      {user.full_name || 'ChatCraft User'}
                    </Text>
                    <View style={styles.emailContainer}>
                      <Mail size={14} color={colors.TEXT_SECONDARY} />
                      <Text style={[styles.profileEmail, { color: colors.TEXT_SECONDARY }]}>{user.email}</Text>
                    </View>
                  </View>
                </View>
              ) : (
                <View style={styles.profileHeader}>
                  <SkeletonLoader width={64} height={64} borderRadius={32} style={{ marginRight: 12 }} />
                  <View style={styles.profileInfo}>
                    <SkeletonLoader width={120} height={18} style={{ marginBottom: 8 }} />
                    <SkeletonLoader width={160} height={14} />
                  </View>
                </View>
              )}
              
              {/* Profile Stats */}
              <View style={styles.profileStats}>
                {user ? (
                  <>
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>{user.streak_days || 0}</Text>
                      <Text style={styles.statLabel}>Day Streak</Text>
                    </View>
                    <View style={styles.statItem}>
                      <Text style={[styles.statNumber, { color: COLORS.SECONDARY_TEAL }]}>
                        {user.average_score ? Math.round(user.average_score) : 0}%
                      </Text>
                      <Text style={styles.statLabel}>Avg Score</Text>
                    </View>
                    <View style={styles.statItem}>
                      <Text style={[styles.statNumber, { color: COLORS.WARNING_YELLOW }]}>
                        {user.total_score || 0}
                      </Text>
                      <Text style={styles.statLabel}>Total Points</Text>
                    </View>
                  </>
                ) : (
                  <>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={40} height={24} style={{ marginBottom: 8 }} />
                      <SkeletonLoader width={60} height={12} />
                    </View>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={40} height={24} style={{ marginBottom: 8 }} />
                      <SkeletonLoader width={60} height={12} />
                    </View>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={40} height={24} style={{ marginBottom: 8 }} />
                      <SkeletonLoader width={60} height={12} />
                    </View>
                  </>
                )}
              </View>
              
              {/* Sign Out Button */}
              {user ? (
                <TouchableOpacity 
                  style={styles.signOutButton}
                  onPress={handleSignOut}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <SkeletonLoader width={80} height={16} style={{ backgroundColor: 'rgba(239,68,68,0.2)' }} />
                  ) : (
                    <>
                      <LogOut size={16} color={COLORS.ERROR_RED} />
                      <Text style={styles.signOutText}>Sign Out</Text>
                    </>
                  )}
                </TouchableOpacity>
              ) : (
                <SkeletonLoader width="100%" height={44} borderRadius={22} />
              )}
            </View>
          </View>
        )}

        {/* Theme Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>Settings</Text>
          <View style={[styles.settingsCard, { backgroundColor: colors.CARD_WHITE }]}>
            <ThemeToggle />
          </View>
        </View>

        {/* App Description */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>About ChatCraft</Text>
          <Text style={[styles.description, { color: colors.TEXT_SECONDARY }]}>
            Practice dating conversations with instant AI feedback. Get real-time coaching on your messages with color-coded responses to improve your conversation skills.
          </Text>
        </View>

        {/* Origin Story */}
        <View style={styles.section}>
          <View style={styles.redditHeader}>
            <Text style={styles.redditIcon}>"</Text>
            <Text style={styles.redditTitle}>Inspired by Reddit</Text>
          </View>
          
          <View style={styles.quoteCard}>
            <Text style={styles.quoteText}>
              "I used to be terrible at texting girls. Then I discovered this trick: I would screenshot conversations and ask my female friends to rate each message green, yellow, or red. Green meant good, yellow was okay, red was bad. After practicing this way for months, my conversation skills improved dramatically..."
            </Text>
          </View>
          
          <Text style={styles.storyExplanation}>
            This app brings that viral Reddit story to life with AI-powered feedback.
          </Text>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <View style={[styles.featureIcon, { backgroundColor: COLORS.SUCCESS_GREEN }]}>
                <MessageCircle size={20} color={COLORS.CARD_WHITE} />
              </View>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Real-time conversation feedback</Text>
                <Text style={styles.featureDescription}>Get instant color-coded feedback on every message</Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <View style={[styles.featureIcon, { backgroundColor: COLORS.WARNING_YELLOW }]}>
                <Target size={20} color={COLORS.CARD_WHITE} />
              </View>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Multiple practice scenarios</Text>
                <Text style={styles.featureDescription}>From first messages to planning dates</Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <View style={[styles.featureIcon, { backgroundColor: COLORS.ERROR_RED }]}>
                <Lightbulb size={20} color={COLORS.CARD_WHITE} />
              </View>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>AI-powered coaching tips</Text>
                <Text style={styles.featureDescription}>Learn what works and what doesn't</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Magically Attribution */}
        <LinearGradient
          colors={['#8b5cf6', '#ec4899']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.magicallyCard}
        >
          <View style={styles.magicallyContent}>
            <Sparkles size={32} color={COLORS.CARD_WHITE} />
            <Text style={styles.magicallyTitle}>Built with Magically</Text>
            <Text style={styles.magicallyDescription}>
              This app was created in minutes using Magically's AI-powered app builder.
            </Text>
            <TouchableOpacity style={styles.magicallyButton} onPress={handleMagicallyPress}>
              <Text style={styles.magicallyButtonText}>Try Magically</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Version Info */}
        <View style={styles.versionSection}>
          <Text style={styles.versionText}>Version 1.0.0</Text>
          <Text style={styles.madeWithLove}>Made with ❤️ using AI</Text>
        </View>
      </ScrollView>

      {/* Custom Sign Out Modal */}
      <SignOutModal
        visible={showSignOutModal}
        onClose={handleCancelSignOut}
        onConfirm={handleConfirmSignOut}
        isLoading={isLoading}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.4,
  },
  logoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fce7f3',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 100,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
  },
  settingsCard: {
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  redditHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  redditIcon: {
    fontSize: 32,
    color: COLORS.PRIMARY_PINK,
    fontWeight: 'bold',
    marginRight: 8,
  },
  redditTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  quoteCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY_PINK,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  quoteText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 22,
    fontStyle: 'italic',
  },
  storyExplanation: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: COLORS.CARD_WHITE,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 18,
  },
  magicallyCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  magicallyContent: {
    alignItems: 'center',
  },
  magicallyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.CARD_WHITE,
    marginTop: 12,
    marginBottom: 8,
  },
  magicallyDescription: {
    fontSize: 14,
    color: COLORS.CARD_WHITE,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
    opacity: 0.9,
  },
  magicallyButton: {
    backgroundColor: COLORS.CARD_WHITE,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  magicallyButtonText: {
    color: '#8b5cf6',
    fontSize: 16,
    fontWeight: '600',
  },
  versionSection: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  versionText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  madeWithLove: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  profileCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: COLORS.BACKGROUND_GRAY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    overflow: 'hidden',
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  emailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileEmail: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: 6,
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#f3f4f6',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY_PINK,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  signOutText: {
    color: COLORS.ERROR_RED,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});
