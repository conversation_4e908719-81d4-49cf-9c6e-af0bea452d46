import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList } from 'react-native';
import { Heart, MessageCircle, Clock, CheckCircle, TrendingUp, Star } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useSessionStore } from '../stores/sessionStore';
import { useUserStore } from '../stores/userStore';
import { format } from 'date-fns';
import { SkeletonLoader } from '../components/ui/SkeletonLoader';

interface ChatScreenProps {
  navigation: any;
}

export default function ChatScreen({ navigation }: ChatScreenProps) {
  const insets = useSafeAreaInsets();
  const { sessions, loadUserSessions, continueSession } = useSessionStore();
  const { user } = useUserStore();
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'active' | 'completed'>('active');

  useEffect(() => {
    const loadData = async () => {
      if (user) {
        setIsLoading(true);
        await loadUserSessions(user.id);
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
    };
    loadData();
  }, [user]);

  const activeSessions = sessions.filter(s => s.status === 'active');
  const completedSessions = sessions.filter(s => s.status === 'completed');

  const getScoreColor = (score: number) => {
    if (score >= 80) return colors.SUCCESS_GREEN;
    if (score >= 60) return colors.WARNING_YELLOW;
    return colors.ERROR_RED;
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    return 'Needs Work';
  };

  const handleContinueChat = async (sessionId: string) => {
    await continueSession(sessionId);
    navigation.navigate('ConversationScreen');
  };

  const averageScore = completedSessions.length > 0 
    ? Math.round(completedSessions.reduce((sum, s) => sum + (s.score?.overall || 0), 0) / completedSessions.length)
    : 0;

  const renderActiveSession = ({ item }: { item: any }) => (
    <View style={[styles.sessionCard, { backgroundColor: colors.CARD_WHITE }]}>
      <View style={styles.sessionHeader}>
        <View style={[styles.activeIndicator, { backgroundColor: colors.SUCCESS_GREEN }]} />
        <Text style={[styles.sessionTitle, { color: colors.TEXT_PRIMARY }]}>{item.scenarioTitle}</Text>
        <Text style={[styles.activeLabel, { color: colors.SUCCESS_GREEN }]}>Active</Text>
      </View>

      <Text style={[styles.sessionTime, { color: colors.TEXT_SECONDARY }]}>
        Started {format(new Date(item.startedAt), 'MMM d, h:mm a')}
      </Text>
      
      <TouchableOpacity
        style={[styles.continueButton, { backgroundColor: colors.PRIMARY_PINK }]}
        onPress={() => handleContinueChat(item.id)}
      >
        <MessageCircle size={16} color={colors.CARD_WHITE} />
        <Text style={[styles.continueButtonText, { color: colors.CARD_WHITE }]}>Continue Chat</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCompletedSession = ({ item }: { item: any }) => (
    <View style={[styles.sessionCard, { backgroundColor: colors.CARD_WHITE }]}>
      <View style={styles.sessionHeader}>
        <CheckCircle size={20} color={colors.SUCCESS_GREEN} />
        <Text style={[styles.sessionTitle, { color: colors.TEXT_PRIMARY }]}>{item.scenarioTitle}</Text>
        <View style={[styles.scoreChip, { backgroundColor: getScoreColor(item.score?.overall || 0) }]}>
          <Text style={[styles.scoreText, { color: colors.CARD_WHITE }]}>{item.score?.overall || 0}%</Text>
        </View>
      </View>
      
      <Text style={[styles.sessionTime, { color: colors.TEXT_SECONDARY }]}>
        Completed {format(new Date(item.completedAt), 'MMM d, h:mm a')}
      </Text>

      <View style={styles.sessionStats}>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: colors.SUCCESS_GREEN }]}>
            {item.score?.positive || 0}
          </Text>
          <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>Great</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: colors.WARNING_YELLOW }]}>
            {item.score?.neutral || 0}
          </Text>
          <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>Good</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: colors.ERROR_RED }]}>
            {item.score?.negative || 0}
          </Text>
          <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>Needs Work</Text>
        </View>
      </View>
      
      <View style={styles.completedBadge}>
        <Text style={[styles.completedText, { color: colors.TEXT_SECONDARY }]}>Completed - Cannot Restart</Text>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND_GRAY }]}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top, backgroundColor: colors.CARD_WHITE }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={[styles.headerTitle, { color: colors.TEXT_PRIMARY }]}>Chat</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Overview Stats */}
        <View style={styles.overviewSection}>
          <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>Your Chat History</Text>
          
          {isLoading ? (
            <View style={styles.statsGrid}>
              <View style={styles.overviewCard}>
                <SkeletonLoader width={24} height={24} borderRadius={12} style={{ marginBottom: 8 }} />
                <SkeletonLoader width={40} height={24} style={{ marginBottom: 4 }} />
                <SkeletonLoader width={80} height={12} />
              </View>
              <View style={styles.overviewCard}>
                <SkeletonLoader width={24} height={24} borderRadius={12} style={{ marginBottom: 8 }} />
                <SkeletonLoader width={50} height={24} style={{ marginBottom: 4 }} />
                <SkeletonLoader width={80} height={12} />
              </View>
            </View>
          ) : (
            <View style={styles.statsGrid}>
              <View style={[styles.overviewCard, { backgroundColor: colors.CARD_WHITE }]}>
                <TrendingUp size={24} color={colors.PRIMARY_PINK} />
                <Text style={[styles.overviewNumber, { color: colors.PRIMARY_PINK }]}>{completedSessions.length}</Text>
                <Text style={[styles.overviewLabel, { color: colors.TEXT_SECONDARY }]}>Chats Completed</Text>
              </View>
              <View style={[styles.overviewCard, { backgroundColor: colors.CARD_WHITE }]}>
                <Star size={24} color={colors.SECONDARY_TEAL} />
                <Text style={[styles.overviewNumber, { color: colors.SECONDARY_TEAL }]}>
                  {averageScore}%
                </Text>
                <Text style={[styles.overviewLabel, { color: colors.TEXT_SECONDARY }]}>Average Score</Text>
              </View>
            </View>
          )}
        </View>

        {/* Tab Navigation */}
        <View style={[styles.tabContainer, { backgroundColor: colors.CARD_WHITE }]}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'active' && { backgroundColor: colors.PRIMARY_PINK }]}
            onPress={() => setActiveTab('active')}
          >
            <MessageCircle size={16} color={activeTab === 'active' ? colors.CARD_WHITE : colors.TEXT_SECONDARY} />
            <Text style={[styles.tabText, { color: activeTab === 'active' ? colors.CARD_WHITE : colors.TEXT_SECONDARY }]}>
              Active ({activeSessions.length})
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'completed' && { backgroundColor: colors.PRIMARY_PINK }]}
            onPress={() => setActiveTab('completed')}
          >
            <CheckCircle size={16} color={activeTab === 'completed' ? colors.CARD_WHITE : colors.TEXT_SECONDARY} />
            <Text style={[styles.tabText, { color: activeTab === 'completed' ? colors.CARD_WHITE : colors.TEXT_SECONDARY }]}>
              Completed ({completedSessions.length})
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContent}>
          {activeTab === 'active' ? (
            /* Active Chats Tab */
            isLoading ? (
              Array.from({ length: 2 }).map((_, index) => (
                <View key={index} style={styles.sessionCard}>
                  <View style={styles.sessionHeader}>
                    <SkeletonLoader width={8} height={8} borderRadius={4} style={{ marginRight: 12 }} />
                    <SkeletonLoader width={150} height={16} style={{ marginRight: 'auto' }} />
                    <SkeletonLoader width={50} height={20} borderRadius={10} />
                  </View>
                  <SkeletonLoader width={120} height={12} style={{ marginVertical: 8 }} />
                  <SkeletonLoader width="100%" height={40} borderRadius={20} />
                </View>
              ))
            ) : (
              activeSessions.length > 0 ? (
                <FlatList
                  data={activeSessions}
                  renderItem={renderActiveSession}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                />
              ) : (
                <View style={styles.emptyState}>
                  <MessageCircle size={48} color={colors.TEXT_SECONDARY} />
                  <Text style={[styles.emptyTitle, { color: colors.TEXT_PRIMARY }]}>No active chats</Text>
                  <Text style={[styles.emptySubtitle, { color: colors.TEXT_SECONDARY }]}>
                    Start a new conversation to begin practicing
                  </Text>
                  <TouchableOpacity
                    style={[styles.startButton, { backgroundColor: colors.PRIMARY_PINK }]}
                    onPress={() => navigation.navigate('PracticeHub')}
                  >
                    <Text style={[styles.startButtonText, { color: colors.CARD_WHITE }]}>Start New Chat</Text>
                  </TouchableOpacity>
                </View>
              )
            )
          ) : (
            /* Completed Chats Tab */
            isLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <View key={index} style={styles.sessionCard}>
                  <View style={styles.sessionHeader}>
                    <SkeletonLoader width={20} height={20} borderRadius={10} style={{ marginRight: 12 }} />
                    <SkeletonLoader width={140} height={16} style={{ marginRight: 'auto' }} />
                    <SkeletonLoader width={60} height={24} borderRadius={12} />
                  </View>
                  <SkeletonLoader width={140} height={12} style={{ marginVertical: 8 }} />
                  <View style={styles.sessionStats}>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={20} height={20} style={{ marginBottom: 4 }} />
                      <SkeletonLoader width={40} height={12} />
                    </View>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={20} height={20} style={{ marginBottom: 4 }} />
                      <SkeletonLoader width={40} height={12} />
                    </View>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={20} height={20} style={{ marginBottom: 4 }} />
                      <SkeletonLoader width={60} height={12} />
                    </View>
                  </View>
                  <SkeletonLoader width="100%" height={32} borderRadius={16} style={{ marginTop: 12 }} />
                </View>
              ))
            ) : (
              completedSessions.length > 0 ? (
                <FlatList
                  data={completedSessions}
                  renderItem={renderCompletedSession}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                />
              ) : (
                <View style={styles.emptyState}>
                  <CheckCircle size={48} color={colors.TEXT_SECONDARY} />
                  <Text style={[styles.emptyTitle, { color: colors.TEXT_PRIMARY }]}>No completed chats yet</Text>
                  <Text style={[styles.emptySubtitle, { color: colors.TEXT_SECONDARY }]}>
                    Complete conversations to see your progress here
                  </Text>
                  <TouchableOpacity
                    style={[styles.startButton, { backgroundColor: colors.PRIMARY_PINK }]}
                    onPress={() => navigation.navigate('PracticeHub')}
                  >
                    <Text style={[styles.startButtonText, { color: colors.CARD_WHITE }]}>Start Practicing</Text>
                  </TouchableOpacity>
                </View>
              )
            )
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.4,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 100,
  },
  overviewSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  overviewCard: {
    flex: 1,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  overviewNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 14,
    textAlign: 'center',
  },
  section: {
    marginBottom: 32,
  },
  sessionCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  sessionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  activeLabel: {
    fontSize: 12,
    fontWeight: '600',
    backgroundColor: '#f0fdf4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  scoreChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  scoreText: {
    fontSize: 12,
    fontWeight: '600',
  },
  sessionTime: {
    fontSize: 14,
    marginBottom: 16,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  sessionStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#f3f4f6',
    marginBottom: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
  },
  completedBadge: {
    backgroundColor: '#f3f4f6',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  completedText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  startButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  activeTab: {
    // backgroundColor will be set dynamically
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  activeTabText: {
    // color will be set dynamically
  },
  tabContent: {
    flex: 1,
  },
});