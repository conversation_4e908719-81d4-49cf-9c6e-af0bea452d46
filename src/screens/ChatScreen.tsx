import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList } from 'react-native';
import { Heart, MessageCircle, Clock, CheckCircle, TrendingUp, Star } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useSessionStore } from '../stores/sessionStore';
import { useUserStore } from '../stores/userStore';
import { format } from 'date-fns';
import { SkeletonLoader } from '../components/ui/SkeletonLoader';

interface ChatScreenProps {
  navigation: any;
}

export default function ChatScreen({ navigation }: ChatScreenProps) {
  const insets = useSafeAreaInsets();
  const { sessions, loadUserSessions, continueSession } = useSessionStore();
  const { user } = useUserStore();
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'active' | 'completed'>('active');

  useEffect(() => {
    const loadData = async () => {
      if (user) {
        setIsLoading(true);
        await loadUserSessions(user.id);
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
    };
    loadData();
  }, [user]);

  const activeSessions = sessions.filter(s => s.status === 'active');
  const completedSessions = sessions.filter(s => s.status === 'completed');

  const getScoreColor = (score: number) => {
    if (score >= 80) return colors.SUCCESS_GREEN;
    if (score >= 60) return colors.WARNING_YELLOW;
    return colors.ERROR_RED;
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    return 'Needs Work';
  };

  const handleContinueChat = async (sessionId: string) => {
    await continueSession(sessionId);
    navigation.navigate('ConversationScreen');
  };

  const averageScore = completedSessions.length > 0 
    ? Math.round(completedSessions.reduce((sum, s) => sum + (s.score?.overall || 0), 0) / completedSessions.length)
    : 0;

  const renderActiveSession = ({ item }: { item: any }) => (
    <View style={styles.sessionCard}>
      <View style={styles.sessionHeader}>
        <View style={styles.activeIndicator} />
        <Text style={styles.sessionTitle}>{item.scenarioTitle}</Text>
        <Text style={styles.activeLabel}>Active</Text>
      </View>
      
      <Text style={styles.sessionTime}>
        Started {format(new Date(item.startedAt), 'MMM d, h:mm a')}
      </Text>
      
      <TouchableOpacity 
        style={styles.continueButton}
        onPress={() => handleContinueChat(item.id)}
      >
        <MessageCircle size={16} color={COLORS.CARD_WHITE} />
        <Text style={styles.continueButtonText}>Continue Chat</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCompletedSession = ({ item }: { item: any }) => (
    <View style={styles.sessionCard}>
      <View style={styles.sessionHeader}>
        <CheckCircle size={20} color={COLORS.SUCCESS_GREEN} />
        <Text style={styles.sessionTitle}>{item.scenarioTitle}</Text>
        <View style={[styles.scoreChip, { backgroundColor: getScoreColor(item.score?.overall || 0) }]}>
          <Text style={styles.scoreText}>{item.score?.overall || 0}%</Text>
        </View>
      </View>
      
      <Text style={styles.sessionTime}>
        Completed {format(new Date(item.completedAt), 'MMM d, h:mm a')}
      </Text>
      
      <View style={styles.sessionStats}>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: COLORS.SUCCESS_GREEN }]}>
            {item.score?.positive || 0}
          </Text>
          <Text style={styles.statLabel}>Great</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: COLORS.WARNING_YELLOW }]}>
            {item.score?.neutral || 0}
          </Text>
          <Text style={styles.statLabel}>Good</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: COLORS.ERROR_RED }]}>
            {item.score?.negative || 0}
          </Text>
          <Text style={styles.statLabel}>Needs Work</Text>
        </View>
      </View>
      
      <View style={styles.completedBadge}>
        <Text style={styles.completedText}>Completed - Cannot Restart</Text>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND_GRAY }]}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top, backgroundColor: colors.CARD_WHITE }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={[styles.headerTitle, { color: colors.TEXT_PRIMARY }]}>Chat</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Overview Stats */}
        <View style={styles.overviewSection}>
          <Text style={styles.sectionTitle}>Your Chat History</Text>
          
          {isLoading ? (
            <View style={styles.statsGrid}>
              <View style={styles.overviewCard}>
                <SkeletonLoader width={24} height={24} borderRadius={12} style={{ marginBottom: 8 }} />
                <SkeletonLoader width={40} height={24} style={{ marginBottom: 4 }} />
                <SkeletonLoader width={80} height={12} />
              </View>
              <View style={styles.overviewCard}>
                <SkeletonLoader width={24} height={24} borderRadius={12} style={{ marginBottom: 8 }} />
                <SkeletonLoader width={50} height={24} style={{ marginBottom: 4 }} />
                <SkeletonLoader width={80} height={12} />
              </View>
            </View>
          ) : (
            <View style={styles.statsGrid}>
              <View style={styles.overviewCard}>
                <TrendingUp size={24} color={COLORS.PRIMARY_PINK} />
                <Text style={styles.overviewNumber}>{completedSessions.length}</Text>
                <Text style={styles.overviewLabel}>Chats Completed</Text>
              </View>
              <View style={styles.overviewCard}>
                <Star size={24} color={COLORS.SECONDARY_TEAL} />
                <Text style={[styles.overviewNumber, { color: COLORS.SECONDARY_TEAL }]}>
                  {averageScore}%
                </Text>
                <Text style={styles.overviewLabel}>Average Score</Text>
              </View>
            </View>
          )}
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'active' && styles.activeTab]}
            onPress={() => setActiveTab('active')}
          >
            <MessageCircle size={16} color={activeTab === 'active' ? COLORS.CARD_WHITE : COLORS.TEXT_SECONDARY} />
            <Text style={[styles.tabText, activeTab === 'active' && styles.activeTabText]}>
              Active ({activeSessions.length})
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
            onPress={() => setActiveTab('completed')}
          >
            <CheckCircle size={16} color={activeTab === 'completed' ? COLORS.CARD_WHITE : COLORS.TEXT_SECONDARY} />
            <Text style={[styles.tabText, activeTab === 'completed' && styles.activeTabText]}>
              Completed ({completedSessions.length})
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContent}>
          {activeTab === 'active' ? (
            /* Active Chats Tab */
            isLoading ? (
              Array.from({ length: 2 }).map((_, index) => (
                <View key={index} style={styles.sessionCard}>
                  <View style={styles.sessionHeader}>
                    <SkeletonLoader width={8} height={8} borderRadius={4} style={{ marginRight: 12 }} />
                    <SkeletonLoader width={150} height={16} style={{ marginRight: 'auto' }} />
                    <SkeletonLoader width={50} height={20} borderRadius={10} />
                  </View>
                  <SkeletonLoader width={120} height={12} style={{ marginVertical: 8 }} />
                  <SkeletonLoader width="100%" height={40} borderRadius={20} />
                </View>
              ))
            ) : (
              activeSessions.length > 0 ? (
                <FlatList
                  data={activeSessions}
                  renderItem={renderActiveSession}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                />
              ) : (
                <View style={styles.emptyState}>
                  <MessageCircle size={48} color={COLORS.TEXT_SECONDARY} />
                  <Text style={styles.emptyTitle}>No active chats</Text>
                  <Text style={styles.emptySubtitle}>
                    Start a new conversation to begin practicing
                  </Text>
                  <TouchableOpacity 
                    style={styles.startButton}
                    onPress={() => navigation.navigate('PracticeHub')}
                  >
                    <Text style={styles.startButtonText}>Start New Chat</Text>
                  </TouchableOpacity>
                </View>
              )
            )
          ) : (
            /* Completed Chats Tab */
            isLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <View key={index} style={styles.sessionCard}>
                  <View style={styles.sessionHeader}>
                    <SkeletonLoader width={20} height={20} borderRadius={10} style={{ marginRight: 12 }} />
                    <SkeletonLoader width={140} height={16} style={{ marginRight: 'auto' }} />
                    <SkeletonLoader width={60} height={24} borderRadius={12} />
                  </View>
                  <SkeletonLoader width={140} height={12} style={{ marginVertical: 8 }} />
                  <View style={styles.sessionStats}>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={20} height={20} style={{ marginBottom: 4 }} />
                      <SkeletonLoader width={40} height={12} />
                    </View>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={20} height={20} style={{ marginBottom: 4 }} />
                      <SkeletonLoader width={40} height={12} />
                    </View>
                    <View style={styles.statItem}>
                      <SkeletonLoader width={20} height={20} style={{ marginBottom: 4 }} />
                      <SkeletonLoader width={60} height={12} />
                    </View>
                  </View>
                  <SkeletonLoader width="100%" height={32} borderRadius={16} style={{ marginTop: 12 }} />
                </View>
              ))
            ) : (
              completedSessions.length > 0 ? (
                <FlatList
                  data={completedSessions}
                  renderItem={renderCompletedSession}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                />
              ) : (
                <View style={styles.emptyState}>
                  <CheckCircle size={48} color={COLORS.TEXT_SECONDARY} />
                  <Text style={styles.emptyTitle}>No completed chats yet</Text>
                  <Text style={styles.emptySubtitle}>
                    Complete conversations to see your progress here
                  </Text>
                  <TouchableOpacity 
                    style={styles.startButton}
                    onPress={() => navigation.navigate('PracticeHub')}
                  >
                    <Text style={styles.startButtonText}>Start Practicing</Text>
                  </TouchableOpacity>
                </View>
              )
            )
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_GRAY,
  },
  header: {
    backgroundColor: COLORS.CARD_WHITE,
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    letterSpacing: -0.4,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 100,
  },
  overviewSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  overviewCard: {
    flex: 1,
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  overviewNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.PRIMARY_PINK,
    marginTop: 8,
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  section: {
    marginBottom: 32,
  },
  sessionCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.SUCCESS_GREEN,
    marginRight: 12,
  },
  sessionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  activeLabel: {
    fontSize: 12,
    color: COLORS.SUCCESS_GREEN,
    fontWeight: '600',
    backgroundColor: '#f0fdf4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  scoreChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  scoreText: {
    color: COLORS.CARD_WHITE,
    fontSize: 12,
    fontWeight: '600',
  },
  sessionTime: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 16,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.PRIMARY_PINK,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  continueButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  sessionStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#f3f4f6',
    marginBottom: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  completedBadge: {
    backgroundColor: '#f3f4f6',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  completedText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  startButton: {
    backgroundColor: COLORS.PRIMARY_PINK,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  startButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.CARD_WHITE,
    marginBottom: 24,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: COLORS.PRIMARY_PINK,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.TEXT_SECONDARY,
    marginLeft: 6,
  },
  activeTabText: {
    color: COLORS.CARD_WHITE,
  },
  tabContent: {
    flex: 1,
  },
});