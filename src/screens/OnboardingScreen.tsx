
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { Heart } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../contexts/ThemeContext';
import { usePracticeStore } from '../stores/practiceStore';
import { STORAGE_KEYS } from '../constants';

interface OnboardingScreenProps {
  onComplete: () => void;
}

export default function OnboardingScreen({ onComplete }: OnboardingScreenProps) {
  const setOnboarded = usePracticeStore(state => state.setOnboarded);
  const insets = useSafeAreaInsets();
  const { colors } = useTheme();

  const handleGetStarted = async () => {
    console.log('🎯 User completing onboarding...');
    
    try {
      // Set in store
      setOnboarded(true);
      
      // Also save directly to AsyncStorage as backup
      await AsyncStorage.setItem(`${STORAGE_KEYS.PRACTICE_STORE}_onboarded`, 'true');
      console.log('🎯 Onboarding saved to both store and AsyncStorage');
      
      // Give a moment for persistence to save
      await new Promise(resolve => setTimeout(resolve, 300));
      
      console.log('🎯 Onboarding completion confirmed, calling onComplete');
      onComplete();
    } catch (error) {
      console.error('🎯 Error saving onboarding state:', error);
      onComplete(); // Continue anyway
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.BACKGROUND_GRAY }]} contentContainerStyle={[styles.contentContainer, { paddingTop: insets.top + 20 }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.logoContainer, { backgroundColor: colors.CARD_WHITE }]}>
          <Heart size={32} color={colors.PRIMARY_PINK} />
        </View>
        <Text style={[styles.logoText, { color: colors.TEXT_PRIMARY }]}>ChatCraft</Text>
      </View>

      {/* Hero Image */}
      <View style={styles.heroContainer}>
        <Image 
          source={{ uri: 'https://trymagically.com/api/media/image?query=' + encodeURIComponent('modern illustration of two people having a conversation with colorful speech bubbles, minimalist style, pink and teal colors') }}
          style={styles.heroImage}
          resizeMode="cover"
        />
      </View>

      {/* Main Content */}
      <View style={styles.mainContent}>
        <Text style={styles.title}>Master the Art of Connection</Text>
        <Text style={styles.subtitle}>
          Practice conversations with AI-powered feedback. Build confidence before your next meaningful connection.
        </Text>

        {/* Inspiration Quote */}
        <View style={styles.quoteCard}>
          <Text style={styles.quoteIcon}>"</Text>
          <Text style={styles.quoteText}>
            I wish I had practiced my conversation skills before that awkward first date...
          </Text>
          <Text style={styles.quoteAttribution}>
            - Real Reddit story that inspired this app
          </Text>
        </View>
      </View>

      {/* CTA Button */}
      <TouchableOpacity style={styles.ctaButton} onPress={handleGetStarted}>
        <Text style={styles.ctaButtonText}>Get Started</Text>
      </TouchableOpacity>

      {/* Footer */}
      <Text style={styles.footer}>Powered by Magically</Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_GRAY,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: COLORS.CARD_WHITE,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  heroContainer: {
    height: 200,
    marginBottom: 40,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: COLORS.CARD_WHITE,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  mainContent: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 40,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  quoteCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 24,
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY_PINK,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  quoteIcon: {
    fontSize: 48,
    color: COLORS.PRIMARY_PINK,
    fontWeight: 'bold',
    lineHeight: 48,
    marginBottom: 8,
  },
  quoteText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    fontStyle: 'italic',
    lineHeight: 24,
    marginBottom: 12,
  },
  quoteAttribution: {
    fontSize: 14,
    color: COLORS.PRIMARY_PINK,
    fontWeight: '600',
  },
  ctaButton: {
    backgroundColor: COLORS.PRIMARY_PINK,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: COLORS.PRIMARY_PINK,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  ctaButtonText: {
    color: COLORS.CARD_WHITE,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  footer: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
});
