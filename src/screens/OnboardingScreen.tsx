
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { Heart } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../contexts/ThemeContext';
import { usePracticeStore } from '../stores/practiceStore';
import { STORAGE_KEYS } from '../constants';

interface OnboardingScreenProps {
  onComplete: () => void;
}

export default function OnboardingScreen({ onComplete }: OnboardingScreenProps) {
  const setOnboarded = usePracticeStore(state => state.setOnboarded);
  const insets = useSafeAreaInsets();
  const { colors } = useTheme();

  const handleGetStarted = async () => {
    console.log('🎯 User completing onboarding...');
    
    try {
      // Set in store
      setOnboarded(true);
      
      // Also save directly to AsyncStorage as backup
      await AsyncStorage.setItem(`${STORAGE_KEYS.PRACTICE_STORE}_onboarded`, 'true');
      console.log('🎯 Onboarding saved to both store and AsyncStorage');
      
      // Give a moment for persistence to save
      await new Promise(resolve => setTimeout(resolve, 300));
      
      console.log('🎯 Onboarding completion confirmed, calling onComplete');
      onComplete();
    } catch (error) {
      console.error('🎯 Error saving onboarding state:', error);
      onComplete(); // Continue anyway
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.BACKGROUND_GRAY }]} contentContainerStyle={[styles.contentContainer, { paddingTop: insets.top + 20 }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.logoContainer, { backgroundColor: colors.CARD_WHITE }]}>
          <Heart size={32} color={colors.PRIMARY_PINK} />
        </View>
        <Text style={[styles.logoText, { color: colors.TEXT_PRIMARY }]}>ChatCraft</Text>
      </View>

      {/* Hero Image */}
      <View style={[styles.heroContainer, { backgroundColor: colors.CARD_WHITE }]}>
        <Image
          source={{ uri: 'https://trymagically.com/api/media/image?query=' + encodeURIComponent('modern illustration of two people having a conversation with colorful speech bubbles, minimalist style, pink and teal colors') }}
          style={styles.heroImage}
          resizeMode="cover"
        />
      </View>

      {/* Main Content */}
      <View style={styles.mainContent}>
        <Text style={[styles.title, { color: colors.TEXT_PRIMARY }]}>Master the Art of Connection</Text>
        <Text style={[styles.subtitle, { color: colors.TEXT_SECONDARY }]}>
          Practice conversations with AI-powered feedback. Build confidence before your next meaningful connection.
        </Text>

        {/* Inspiration Quote */}
        <View style={[styles.quoteCard, { backgroundColor: colors.CARD_WHITE, borderLeftColor: colors.PRIMARY_PINK }]}>
          <Text style={[styles.quoteIcon, { color: colors.PRIMARY_PINK }]}>"</Text>
          <Text style={[styles.quoteText, { color: colors.TEXT_PRIMARY }]}>
            I wish I had practiced my conversation skills before that awkward first date...
          </Text>
          <Text style={[styles.quoteAttribution, { color: colors.PRIMARY_PINK }]}>
            - Real Reddit story that inspired this app
          </Text>
        </View>
      </View>

      {/* CTA Button */}
      <TouchableOpacity style={[styles.ctaButton, { backgroundColor: colors.PRIMARY_PINK, shadowColor: colors.PRIMARY_PINK }]} onPress={handleGetStarted}>
        <Text style={[styles.ctaButtonText, { color: colors.CARD_WHITE }]}>Get Started</Text>
      </TouchableOpacity>

      {/* Footer */}
      <Text style={[styles.footer, { color: colors.TEXT_SECONDARY }]}>Powered by Magically</Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  heroContainer: {
    height: 200,
    marginBottom: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  mainContent: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 40,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  quoteCard: {
    padding: 24,
    borderRadius: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  quoteIcon: {
    fontSize: 48,
    fontWeight: 'bold',
    lineHeight: 48,
    marginBottom: 8,
  },
  quoteText: {
    fontSize: 16,
    fontStyle: 'italic',
    lineHeight: 24,
    marginBottom: 12,
  },
  quoteAttribution: {
    fontSize: 14,
    fontWeight: '600',
  },
  ctaButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    marginBottom: 24,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  ctaButtonText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  footer: {
    fontSize: 14,
    textAlign: 'center',
  },
});
