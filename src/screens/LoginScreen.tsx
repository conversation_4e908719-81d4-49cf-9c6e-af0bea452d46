import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, Alert } from 'react-native';
import { Heart, Sparkles, Target, TrendingUp } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../contexts/ThemeContext';
import { useUserStore } from '../stores/userStore';
import Svg, { Path } from 'react-native-svg';
import { SkeletonLoader } from '../components/ui/SkeletonLoader';

export default function LoginScreen() {
  const insets = useSafeAreaInsets();
  const [isLoading, setIsLoading] = useState(false);
  const signInWithGoogle = useUserStore(state => state.signInWithGoogle);
  const { colors, isDark } = useTheme();

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      console.log('🔐 LoginScreen: Starting Google authentication...');
      await signInWithGoogle();
      console.log('✅ LoginScreen: Google authentication completed successfully');
    } catch (error) {
      console.error('❌ LoginScreen: Authentication error:', error);
      Alert.alert(
        'Authentication Failed',
        error.message || 'Unable to sign in with Google. Please check your connection and try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: colors.BACKGROUND_LIGHT }]}>
      {/* Background Gradient */}
      <LinearGradient
        colors={isDark ? [colors.BACKGROUND_LIGHT, colors.BACKGROUND_GRAY, colors.BACKGROUND_DARK] : ['#ffffff', '#f8fafc', '#f1f5f9']}
        style={styles.backgroundGradient}
      />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Section */}
        <View style={styles.heroSection}>
          {/* App Icon */}
          <View style={styles.appIconContainer}>
            <LinearGradient
              colors={[colors.PRIMARY_PINK, '#ec4899']}
              style={styles.appIcon}
            >
              <Heart size={28} color="white" strokeWidth={2.5} />
            </LinearGradient>
          </View>

          {/* App Name & Tagline */}
          <Text style={[styles.appName, { color: colors.TEXT_PRIMARY }]}>ChatCraft</Text>
          <Text style={[styles.heroTagline, { color: colors.TEXT_SECONDARY }]}>Master the Art of Connection</Text>
        </View>

        {/* Welcome Content */}
        <View style={styles.welcomeContent}>
          <Text style={[styles.welcomeTitle, { color: colors.TEXT_PRIMARY }]}>Welcome to ChatCraft</Text>
          <Text style={[styles.welcomeDescription, { color: colors.TEXT_SECONDARY }]}>
            Practice dating conversations with AI-powered feedback. Build confidence before your next meaningful connection.
          </Text>
        </View>

        {/* Primary CTA Section - Login */}
        <View style={styles.primaryCtaSection}>
          <View style={[styles.primaryCtaCard, { backgroundColor: colors.CARD_WHITE }]}>
            <Text style={[styles.primaryCtaTitle, { color: colors.TEXT_PRIMARY }]}>Ready to get started?</Text>
            <Text style={[styles.primaryCtaSubtitle, { color: colors.TEXT_SECONDARY }]}>
              Sign in with Google to start practicing and save your progress
            </Text>
            
            {/* Google Sign In Button */}
            <TouchableOpacity 
              style={[styles.primaryGoogleButton, isLoading && styles.primaryGoogleButtonDisabled]}
              onPress={handleGoogleSignIn}
              disabled={isLoading}
            >
              {!isLoading && (
                <View style={styles.googleIconContainer}>
                  <Svg width={20} height={20} viewBox="0 0 24 24">
                    <Path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <Path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <Path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <Path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </Svg>
                </View>
              )}
              
              {isLoading && (
                <SkeletonLoader 
                  width={20} 
                  height={20} 
                  borderRadius={10}
                  style={{ marginRight: 12, backgroundColor: 'rgba(60,64,67,0.2)' }} 
                />
              )}
              
              <Text style={[styles.primaryGoogleButtonText, isLoading && styles.primaryGoogleButtonTextLoading]}>
                {isLoading ? 'Signing in...' : 'Continue with Google'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Hero Image Card */}
        <View style={styles.heroImageCard}>
          <Image 
            source={{ uri: 'https://magically.life/api/media/image?query=' + encodeURIComponent('modern illustration of people having conversations with speech bubbles, dating app concept, pink and teal colors, minimalist style') }}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.3)']}
            style={styles.heroImageOverlay}
          />
        </View>

        {/* Features Grid */}
        <View style={styles.featuresGrid}>
          <View style={styles.featureCard}>
            <View style={[styles.featureIconContainer, { backgroundColor: '#fef3c7' }]}>
              <Sparkles size={20} color="#f59e0b" strokeWidth={2} />
            </View>
            <Text style={styles.featureTitle}>Real-time Feedback</Text>
            <Text style={styles.featureDescription}>Get instant AI-powered conversation analysis</Text>
          </View>
          
          <View style={styles.featureCard}>
            <View style={[styles.featureIconContainer, { backgroundColor: '#fecaca' }]}>
              <Target size={20} color="#ef4444" strokeWidth={2} />
            </View>
            <Text style={styles.featureTitle}>Practice Scenarios</Text>
            <Text style={styles.featureDescription}>Multiple dating conversation contexts</Text>
          </View>
          
          <View style={styles.featureCard}>
            <View style={[styles.featureIconContainer, { backgroundColor: '#d1fae5' }]}>
              <TrendingUp size={20} color="#10b981" strokeWidth={2} />
            </View>
            <Text style={styles.featureTitle}>Track Progress</Text>
            <Text style={styles.featureDescription}>Monitor your improvement over time</Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerBrand}>Powered by Magically</Text>
          <Text style={styles.footerTerms}>
            By signing in, you agree to our terms of service and privacy policy
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  
  // Hero Section
  heroSection: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
  },
  appIconContainer: {
    marginBottom: 20,
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.PRIMARY_PINK,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  appName: {
    fontSize: 32,
    fontWeight: '700',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  heroTagline: {
    fontSize: 17,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '400',
    letterSpacing: -0.2,
  },
  
  // Hero Image
  heroImageCard: {
    height: 180,
    marginHorizontal: 4,
    marginBottom: 32,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: COLORS.CARD_WHITE,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 6,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroImageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  
  // Welcome Content
  welcomeContent: {
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 8,
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 16,
    letterSpacing: -0.4,
  },
  welcomeDescription: {
    fontSize: 17,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 26,
    fontWeight: '400',
    letterSpacing: -0.2,
  },
  
  // Features Grid
  featuresGrid: {
    marginBottom: 32,
    gap: 14,
  },
  featureCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    borderWidth: 0.5,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  featureIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 6,
    textAlign: 'center',
    letterSpacing: -0.2,
  },
  featureDescription: {
    fontSize: 15,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    fontWeight: '400',
  },
  
  // Primary CTA Section (Login)
  primaryCtaSection: {
    marginBottom: 40,
  },
  primaryCtaCard: {
    backgroundColor: COLORS.CARD_WHITE,
    padding: 28,
    borderRadius: 20,
    alignItems: 'center',
    shadowColor: COLORS.PRIMARY_PINK,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(236,72,153,0.1)',
  },
  primaryCtaTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
    textAlign: 'center',
    letterSpacing: -0.3,
  },
  primaryCtaSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 28,
    fontWeight: '400',
  },
  
  // Primary Google Button
  primaryGoogleButton: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 14,
    width: '100%',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
  },
  primaryGoogleButtonDisabled: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
    shadowOpacity: 0.06,
  },
  googleIconContainer: {
    marginRight: 12,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryGoogleButtonText: {
    color: '#374151',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
  primaryGoogleButtonTextLoading: {
    color: '#9CA3AF',
  },
  
  // Footer
  footer: {
    alignItems: 'center',
    paddingTop: 24,
    paddingBottom: 8,
  },
  footerBrand: {
    fontSize: 15,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 12,
    fontWeight: '500',
  },
  footerTerms: {
    fontSize: 13,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 18,
    fontWeight: '400',
    opacity: 0.8,
  },
});