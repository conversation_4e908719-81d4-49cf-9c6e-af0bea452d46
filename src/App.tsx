
import React from 'react';
import { DarkTheme, DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Toaster } from 'sonner-native';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import RootNavigator from './navigation';
import './utils/authCallback'; // Initialize auth callback handler

// Inner app component that uses theme
function AppContent() {
    const { colors, isDark } = useTheme();

    // Always extend the base theme from react.navigation.
    // Otherwise, error such as cannot read property 'n.medium' of undefined will occur which basically means the fonts property is missing from the theme.
    const navigationTheme = {
        ...(isDark ? DarkTheme : DefaultTheme),
        colors: {
            ...(isDark ? DarkTheme.colors : DefaultTheme.colors),
            background: colors.BACKGROUND_GRAY,
            card: colors.CARD_WHITE,
            primary: colors.PRIMARY_PINK,
            text: colors.TEXT_PRIMARY,
            border: colors.BACKGROUND_DARK,
        },
    };

    return (
        <NavigationContainer theme={navigationTheme}>
            <StatusBar style={isDark ? "light" : "dark"} />
            <Toaster theme={isDark ? "dark" : "light"} richColors />
            <RootNavigator />
        </NavigationContainer>
    );
}

/**
 * ChatCraft - Master the Art of Connection
 * A conversation practice app with AI-powered feedback
 */
export default function App() {
    return (
        <SafeAreaProvider>
            <ThemeProvider>
                <AppContent />
            </ThemeProvider>
        </SafeAreaProvider>
    );
}
