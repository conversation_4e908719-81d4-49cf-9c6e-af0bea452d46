import React, { createContext, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { useThemeStore } from '../stores/themeStore';
import { LIGHT_THEME, DARK_THEME } from '../constants/colors';

interface ThemeContextType {
  colors: typeof LIGHT_THEME;
  isDark: boolean;
  mode: 'light' | 'dark' | 'system';
  setTheme: (mode: 'light' | 'dark' | 'system') => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const { 
    mode, 
    isDark, 
    colors, 
    setTheme, 
    toggleTheme, 
    updateSystemTheme 
  } = useThemeStore();

  // Update theme when system color scheme changes
  useEffect(() => {
    const systemIsDark = systemColorScheme === 'dark';
    updateSystemTheme(systemIsDark);
  }, [systemColorScheme, updateSystemTheme]);

  const value: ThemeContextType = {
    colors,
    isDark,
    mode,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
