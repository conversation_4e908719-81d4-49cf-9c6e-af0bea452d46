import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';
import { UserService } from '../services/userService';
import { getCurrentUser, signInWithGoogle, signOut, supabase } from '../services/supabase';
import { Database } from '../types/database';

type UserRow = Database['public']['Tables']['users']['Row'];

interface UserState {
  // User data
  user: UserRow | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Local progress (used when offline)
  localProgress: {
    scenariosCompleted: number;
    messagesPracticed: number;
    completedScenarios: string[];
    totalScore: number;
    averageScore: number;
    streakDays: number;
    lastActiveDate: Date | null;
  };
  
  // Sync state
  lastSyncAt: Date | null;
  needsSync: boolean;
}

interface UserActions {
  // Authentication
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  loadUser: () => Promise<void>;
  
  // Profile management
  updateProfile: (updates: Partial<UserRow>) => Promise<void>;
  updatePreferences: (preferences: Record<string, any>) => Promise<void>;
  
  // Progress tracking
  incrementScenarioCompleted: (scenarioId: string) => void;
  incrementMessagePracticed: () => void;
  updateScore: (score: number) => void;
  updateStreak: () => void;
  
  // Sync
  syncUserData: () => Promise<void>;
  markNeedsSync: () => void;
}

type UserStore = UserState & UserActions;

export const useUserStore = create<UserStore>()(persist(
  (set, get) => ({
    // Initial state
    user: null,
    isAuthenticated: false,
    isLoading: false,
    localProgress: {
      scenariosCompleted: 0,
      messagesPracticed: 0,
      completedScenarios: [],
      totalScore: 0,
      averageScore: 0,
      streakDays: 0,
      lastActiveDate: null
    },
    lastSyncAt: null,
    needsSync: false,

    // Authentication actions
    signInWithGoogle: async () => {
      set({ isLoading: true });
      try {
        console.log('Starting Google sign in...');
        const result = await signInWithGoogle();
        
        if (result.error) {
          console.error('Google sign in result error:', result.error);
          throw new Error(result.error.message);
        }
        
        console.log('Google sign in successful!');
        // Note: loadUser will be called automatically by auth state change
        // No need to call it manually here to avoid race conditions
      } catch (error) {
        console.error('Google sign in error:', error);
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    signOut: async () => {
      set({ isLoading: true });
      try {
        console.log('🚪 Starting user signout...');
        
        // Call Supabase signOut
        const { error } = await signOut();
        if (error) {
          console.error('❌ Supabase signout error:', error);
          throw error;
        }
        
        console.log('✅ Supabase signout successful');
        
        // Clear all user state
        set({ 
          user: null, 
          isAuthenticated: false,
          lastSyncAt: null,
          needsSync: false,
          localProgress: {
            scenariosCompleted: 0,
            messagesPracticed: 0,
            completedScenarios: [],
            totalScore: 0,
            averageScore: 0,
            streakDays: 0,
            lastActiveDate: null
          }
        });
        
        // Clear AsyncStorage user data
        await AsyncStorage.removeItem(STORAGE_KEYS.USER_STORE);
        console.log('✅ User data cleared from storage');
        
        // Clear session storage directly to avoid circular imports
        await AsyncStorage.removeItem(STORAGE_KEYS.PRACTICE_STORE);
        console.log('✅ Session data cleared from storage');
        
      } catch (error) {
        console.error('❌ Sign out error:', error);
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    loadUser: async () => {
      // Prevent multiple simultaneous loadUser calls
      const currentState = get();
      if (currentState.isLoading) {
        console.log('⏳ loadUser already in progress, skipping...');
        return;
      }
      
      set({ isLoading: true });
      try {
        // Check for stored tokens on web (from auth callback)
        if (typeof window !== 'undefined' && window.sessionStorage) {
          const storedTokens = sessionStorage.getItem('supabase_auth_tokens');
          if (storedTokens) {
            console.log('🔄 Found stored auth tokens, setting session...');
            const tokens = JSON.parse(storedTokens);
            if (tokens.access_token && supabase) {
              await supabase.auth.setSession({
                access_token: tokens.access_token,
                refresh_token: tokens.refresh_token
              });
              sessionStorage.removeItem('supabase_auth_tokens');
            }
          }
        }
        
        const authUser = await getCurrentUser();
        if (authUser) {
          // Try to get user profile from database
          let userProfile = await UserService.getUser(authUser.id);
          
          if (!userProfile) {
            // Create user profile if doesn't exist
            const { localProgress } = get();
            userProfile = await UserService.upsertUser({
              id: authUser.id,
              email: authUser.email || '',
              full_name: authUser.user_metadata?.full_name || null,
              avatar_url: authUser.user_metadata?.avatar_url || null,
              total_score: localProgress.totalScore,
              average_score: localProgress.averageScore,
              streak_days: localProgress.streakDays,
              last_active_date: localProgress.lastActiveDate?.toISOString() || null
            });
          }
          
          set({ 
            user: userProfile, 
            isAuthenticated: true,
            needsSync: true // Sync local data with server
          });
        } else {
          set({ user: null, isAuthenticated: false });
        }
      } catch (error) {
        console.error('Load user error:', error);
        set({ user: null, isAuthenticated: false });
      } finally {
        set({ isLoading: false });
      }
    },

    // Profile management
    updateProfile: async (updates: Partial<UserRow>) => {
      const { user } = get();
      if (!user) return;

      try {
        const updatedUser = await UserService.upsertUser({
          ...user,
          ...updates
        });
        
        if (updatedUser) {
          set({ user: updatedUser });
        }
      } catch (error) {
        console.error('Update profile error:', error);
        // Update locally if server fails
        set({ 
          user: user ? { ...user, ...updates } : null,
          needsSync: true
        });
      }
    },

    updatePreferences: async (preferences: Record<string, any>) => {
      const { user } = get();
      if (!user) return;

      try {
        const updatedUser = await UserService.updateUserPreferences(user.id, preferences);
        if (updatedUser) {
          set({ user: updatedUser });
        }
      } catch (error) {
        console.error('Update preferences error:', error);
        // Update locally if server fails
        set({ 
          user: user ? { ...user, preferences } : null,
          needsSync: true
        });
      }
    },

    // Progress tracking
    incrementScenarioCompleted: (scenarioId: string) => {
      set(state => {
        const newCompletedScenarios = state.localProgress.completedScenarios.includes(scenarioId)
          ? state.localProgress.completedScenarios
          : [...state.localProgress.completedScenarios, scenarioId];
        
        const newProgress = {
          ...state.localProgress,
          scenariosCompleted: newCompletedScenarios.length,
          completedScenarios: newCompletedScenarios,
          lastActiveDate: new Date()
        };

        return {
          localProgress: newProgress,
          needsSync: true
        };
      });
    },

    incrementMessagePracticed: () => {
      set(state => ({
        localProgress: {
          ...state.localProgress,
          messagesPracticed: state.localProgress.messagesPracticed + 1,
          lastActiveDate: new Date()
        },
        needsSync: true
      }));
    },

    updateScore: (score: number) => {
      set(state => {
        const currentTotal = state.localProgress.totalScore;
        const currentCount = state.localProgress.messagesPracticed;
        const newTotal = currentTotal + score;
        const newAverage = currentCount > 0 ? newTotal / currentCount : score;

        return {
          localProgress: {
            ...state.localProgress,
            totalScore: newTotal,
            averageScore: newAverage,
            lastActiveDate: new Date()
          },
          needsSync: true
        };
      });
    },

    updateStreak: () => {
      set(state => {
        const today = new Date();
        const lastActive = state.localProgress.lastActiveDate;
        
        let newStreak = state.localProgress.streakDays;
        
        if (lastActive) {
          const daysDiff = Math.floor((today.getTime() - lastActive.getTime()) / (1000 * 60 * 60 * 24));
          if (daysDiff === 1) {
            // Consecutive day
            newStreak += 1;
          } else if (daysDiff > 1) {
            // Streak broken
            newStreak = 1;
          }
          // Same day = no change
        } else {
          // First time
          newStreak = 1;
        }

        return {
          localProgress: {
            ...state.localProgress,
            streakDays: newStreak,
            lastActiveDate: today
          },
          needsSync: true
        };
      });
    },

    // Sync
    syncUserData: async () => {
      const { user, localProgress, needsSync } = get();
      if (!user || !needsSync) return;

      try {
        await UserService.updateUserProgress(user.id, {
          totalScore: localProgress.totalScore,
          averageScore: localProgress.averageScore,
          streakDays: localProgress.streakDays,
          lastActiveDate: localProgress.lastActiveDate?.toISOString() || null
        });
        
        set({ 
          lastSyncAt: new Date(),
          needsSync: false
        });
      } catch (error) {
        console.error('Sync user data error:', error);
        // Keep needsSync true to retry later
      }
    },

    markNeedsSync: () => {
      set({ needsSync: true });
    }
  }),
  {
    name: STORAGE_KEYS.AUTH_STORE,
    storage: {
      getItem: async (name: string) => {
        const value = await AsyncStorage.getItem(name);
        if (!value) return null;

        const parsed = JSON.parse(value);
        // Convert lastActiveDate string back to Date object
        if (parsed.state?.localProgress?.lastActiveDate) {
          parsed.state.localProgress.lastActiveDate = new Date(parsed.state.localProgress.lastActiveDate);
        }
        if (parsed.state?.lastSyncAt) {
          parsed.state.lastSyncAt = new Date(parsed.state.lastSyncAt);
        }
        return parsed;
      },
      setItem: async (name: string, value: any) => {
        await AsyncStorage.setItem(name, JSON.stringify(value));
      },
      removeItem: async (name: string) => {
        await AsyncStorage.removeItem(name);
      }
    },
    // Don't persist sensitive auth state
    partialize: (state) => ({
      localProgress: state.localProgress,
      lastSyncAt: state.lastSyncAt,
      needsSync: state.needsSync
    })
  }
));