import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';
import { LIGHT_THEME, DARK_THEME } from '../constants/colors';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
  colors: typeof LIGHT_THEME;
}

interface ThemeActions {
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  updateSystemTheme: (isDarkSystem: boolean) => void;
}

type ThemeStore = ThemeState & ThemeActions;

// Helper function to determine if dark mode should be active
const getIsDark = (mode: ThemeMode, systemIsDark: boolean = false): boolean => {
  switch (mode) {
    case 'dark':
      return true;
    case 'light':
      return false;
    case 'system':
      return systemIsDark;
    default:
      return false;
  }
};

// Helper function to get colors based on dark mode state
const getColors = (isDark: boolean) => {
  return isDark ? DARK_THEME : LIGHT_THEME;
};

export const useThemeStore = create<ThemeStore>()(persist(
  (set, get) => ({
    // Initial state
    mode: 'system',
    isDark: false,
    colors: LIGHT_THEME,

    // Actions
    setTheme: (mode: ThemeMode) => {
      const systemIsDark = get().isDark; // Use current system state
      const isDark = getIsDark(mode, systemIsDark);
      const colors = getColors(isDark);
      
      set({ 
        mode, 
        isDark, 
        colors 
      });
    },

    toggleTheme: () => {
      const currentMode = get().mode;
      let newMode: ThemeMode;
      
      if (currentMode === 'system') {
        // If system, toggle to opposite of current appearance
        newMode = get().isDark ? 'light' : 'dark';
      } else {
        // If manual mode, toggle between light and dark
        newMode = currentMode === 'light' ? 'dark' : 'light';
      }
      
      get().setTheme(newMode);
    },

    updateSystemTheme: (systemIsDark: boolean) => {
      const currentMode = get().mode;
      const isDark = getIsDark(currentMode, systemIsDark);
      const colors = getColors(isDark);
      
      set({ 
        isDark, 
        colors 
      });
    },
  }),
  {
    name: STORAGE_KEYS.THEME_STORE,
    storage: {
      getItem: async (name: string) => {
        const value = await AsyncStorage.getItem(name);
        return value ? JSON.parse(value) : null;
      },
      setItem: async (name: string, value: any) => {
        await AsyncStorage.setItem(name, JSON.stringify(value));
      },
      removeItem: async (name: string) => {
        await AsyncStorage.removeItem(name);
      }
    },
    // Only persist the theme mode, not the computed state
    partialize: (state) => ({
      mode: state.mode
    })
  }
));
