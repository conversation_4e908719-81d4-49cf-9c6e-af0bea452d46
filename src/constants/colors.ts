
// Theme colors for light and dark modes
export const LIGHT_THEME = {
  // Primary colors from requirements
  PRIMARY_PINK: '#ec4899',
  SECONDARY_TEAL: '#14b8a6',

  // Background colors
  BACKGROUND_LIGHT: '#f8fafc', // Softer, less bright background
  BACKGROUND_GRAY: '#f1f5f9', // Slightly darker for better contrast
  BACKGROUND_DARK: '#e2e8f0', // For subtle variations
  CARD_WHITE: '#ffffff',
  CARD_SOFT: '#fefefe', // Softer white for less harsh contrast

  // Text colors
  TEXT_PRIMARY: '#1e293b', // Slightly softer black
  TEXT_SECONDARY: '#64748b', // Better contrast secondary text
  TEXT_MUTED: '#94a3b8', // For very subtle text

  // Chat specific colors
  CHAT_BUBBLE_USER: '#ec4899', // Keep user messages pink
  CHAT_BUBBLE_AI: '#f8fafc', // Very soft background for AI
  CHAT_BUBBLE_AI_BORDER: '#e2e8f0', // Subtle border for AI messages

  // Feedback colors
  SUCCESS_GREEN: '#10b981',
  WARNING_YELLOW: '#f59e0b',
  ERROR_RED: '#ef4444',
};

export const DARK_THEME = {
  // Primary colors (slightly adjusted for dark mode)
  PRIMARY_PINK: '#f472b6',
  SECONDARY_TEAL: '#2dd4bf',

  // Background colors
  BACKGROUND_LIGHT: '#0f172a', // Dark slate
  BACKGROUND_GRAY: '#1e293b', // Darker slate
  BACKGROUND_DARK: '#334155', // Medium slate
  CARD_WHITE: '#1e293b', // Dark card background
  CARD_SOFT: '#334155', // Softer dark card

  // Text colors
  TEXT_PRIMARY: '#f1f5f9', // Light text
  TEXT_SECONDARY: '#cbd5e1', // Secondary light text
  TEXT_MUTED: '#94a3b8', // Muted text (same as light)

  // Chat specific colors
  CHAT_BUBBLE_USER: '#f472b6', // Lighter pink for dark mode
  CHAT_BUBBLE_AI: '#334155', // Dark background for AI
  CHAT_BUBBLE_AI_BORDER: '#475569', // Darker border for AI messages

  // Feedback colors (slightly brighter for dark mode)
  SUCCESS_GREEN: '#34d399',
  WARNING_YELLOW: '#fbbf24',
  ERROR_RED: '#f87171',
};

// Legacy export for backward compatibility
export const COLORS = LIGHT_THEME;
